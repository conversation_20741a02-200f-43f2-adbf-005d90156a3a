defmodule Repobot.Workers.EventHandler do
  @moduledoc """
  Common behavior and utilities for event handler workers.

  This module provides a standardized way to handle events in Oban workers,
  including common error handling, logging, and event status management.

  ## Usage

      defmodule MyApp.Workers.EventHandlers.MyEventHandler do
        use Repobot.Workers.EventHandler

        @impl true
        def handle(%Events.Event{} = event) do
          # Your event handling logic here
          # Return :ok, {:ok, result}, or {:error, reason}
          :ok
        end
      end

  The EventHandler behavior handles:
  - Event retrieval and validation
  - Event status updates (completed/failed)
  - Structured logging
  - Error handling and recovery
  - Special cases like deleted events (for repository deletion)
  """

  alias Repobot.Events

  @doc """
  Handle an event and return a result.

  ## Return values
  - `:ok` - Event processed successfully
  - `{:ok, result}` - Event processed successfully with result data
  - `{:error, reason}` - Event processing failed
  """
  @callback handle(Events.Event.t()) :: :ok | {:ok, any()} | {:error, any()}

  defmacro __using__(opts) do
    # Extract queue and max_attempts from opts, with defaults
    queue = Keyword.get(opts, :queue, :default)
    max_attempts = Keyword.get(opts, :max_attempts, 3)

    quote do
      use Repobot.Workers.Worker, queue: unquote(queue), max_attempts: unquote(max_attempts)

      alias Repobot.Events

      @behaviour Repobot.Workers.EventHandler

      @impl Oban.Worker
      def perform(%Oban.Job{args: %{"event_id" => event_id}} = job) do
        log_job_start(job)

        case Events.get_event(event_id) do
          nil ->
            reason = "Event not found: #{event_id}"
            log_job_error(job, reason)
            {:error, reason}

          event ->
            result = handle(event)
            handle_event_result(result, event, job, event_id)
        end
      end

      def perform(%Oban.Job{} = job) do
        log_job_start(job)
        reason = "Missing event_id in job arguments"
        log_job_error(job, reason, %{received_args: job.args})
        {:error, reason}
      end

      # Helper function to handle different result types from handle/1
      defp handle_event_result(result, event, job, event_id) do
        case result do
          success when success == :ok or (is_tuple(success) and elem(success, 0) == :ok) ->
            # Try to update event status to completed, but handle case where event was deleted
            try do
              Events.update_event_status(event, "completed")
            rescue
              Ecto.StaleEntryError ->
                Logger.info("Event was deleted as part of cleanup",
                  event_id: event_id,
                  event_type: event.type
                )
            end

            log_job_success(job, %{event_id: event_id, event_type: event.type})
            :ok

          {:error, reason} ->
            # Update event status to failed
            Events.update_event_status(event, "failed")
            log_job_error(job, reason, %{event_id: event_id, event_type: event.type})
            {:error, reason}
        end
      end
    end
  end
end
